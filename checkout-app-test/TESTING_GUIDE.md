# Testing Guide for Israeli Cities Checkout Extension

## Quick Start

1. **Start Development Server**:
   ```bash
   cd checkout-app-test
   npm run dev
   ```

2. **Open Preview**:
   - Press `p` in the terminal to open developer console
   - Click on the preview link for your extension

## Testing Steps

### 1. Basic Functionality Test
1. Navigate to checkout
2. Set country to Israel (IL)
3. Verify that the city dropdown appears above the address form
4. Select a city from the dropdown
5. Verify that the city field in the address form is automatically populated

### 2. Country-Specific Test
1. Set country to a different country (e.g., US, CA)
2. Verify that the city dropdown does NOT appear
3. Change back to Israel
4. Verify that the dropdown reappears

### 3. Localization Test
1. Change browser language to Hebrew
2. Verify Hebrew text displays correctly
3. Test with English and French if available

### 4. Error Handling Test
1. Check browser console for any errors
2. Try selecting empty value from dropdown
3. Verify graceful handling of API errors

## Expected Behavior

✅ **Should Work**:
- Dropdown appears only for Israeli addresses
- City selection updates shipping address
- Hebrew text displays correctly
- No console errors

❌ **Should Not Happen**:
- Dropdown appears for non-Israeli countries
- City selection doesn't update address
- Extension crashes or shows errors
- Performance issues

## Troubleshooting

### Extension Not Showing
- Check that country is set to Israel (IL)
- Verify `protected_customer_data = true` in config
- Check browser console for errors

### City Not Updating
- Verify API permissions
- Check network tab for failed requests
- Ensure selected city value is not empty

### Performance Issues
- Check if extension renders only for IL country
- Verify no unnecessary re-renders
- Monitor console for warnings

## Files Modified

- `src/Checkout.tsx` - Main extension logic
- `shopify.extension.toml` - Configuration
- `locales/en.default.json` - English translations
- `locales/fr.json` - French translations
- `locales/he.json` - Hebrew translations (new)

## Key Features Implemented

1. **Israeli Cities Dropdown**: 40+ major cities in Hebrew
2. **Country-Specific Display**: Only shows for Israel
3. **Automatic Address Update**: Uses Shopify's shipping address API
4. **Multilingual Support**: Hebrew, English, French
5. **Error Handling**: Graceful fallbacks and logging
6. **Performance Optimized**: Conditional rendering
