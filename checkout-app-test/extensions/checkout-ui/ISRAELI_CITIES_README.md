# Israeli Cities Dropdown for Shopify Checkout

This Shopify Checkout UI Extension adds a dropdown selector for Israeli cities that automatically updates the shipping address city field during checkout.

## Features

- **Israeli Cities Dropdown**: Pre-populated list of major Israeli cities in Hebrew
- **Automatic Address Update**: Selected city is automatically applied to the shipping address
- **Country-Specific**: Only shows for Israeli addresses (IL country code)
- **Multilingual Support**: Supports Hebrew, English, and French localization
- **Shopify Guidelines Compliant**: Built according to Shopify's best practices

## How It Works

1. The extension renders before the standard delivery address form
2. When a customer selects Israel as their country, the city dropdown appears
3. Selecting a city automatically updates the shipping address city field
4. The extension uses the `useApplyShippingAddressChange` API to update the address

## Technical Implementation

### Extension Target
- **Target**: `purchase.checkout.delivery-address.render-before`
- **Purpose**: Renders before the standard shipping address form

### Key Components
- **Select Component**: Shopify's native Select component for the dropdown
- **Israeli Cities List**: Comprehensive list of major Israeli cities
- **Address Update API**: Uses `useApplyShippingAddressChange` to modify shipping address

### Capabilities Required
- `protected_customer_data = true` - Required to access and modify shipping address data

## Cities Included

The extension includes 40+ major Israeli cities including:
- תל אביב (Tel Aviv)
- ירושלים (Jerusalem)
- חיפה (Haifa)
- ראשון לציון (Rishon LeZion)
- אשדוד (Ashdod)
- נתניה (Netanya)
- באר שבע (Beer Sheva)
- And many more...

## Localization

The extension supports multiple languages:
- **Hebrew** (`he.json`): Native Hebrew text
- **English** (`en.default.json`): English fallback
- **French** (`fr.json`): French translation

## Installation & Development

1. **Install Dependencies**:
   ```bash
   npm install
   ```

2. **Start Development Server**:
   ```bash
   shopify app dev
   ```

3. **Deploy**:
   ```bash
   shopify app deploy
   ```

## Configuration

The extension is configured in `shopify.extension.toml`:

```toml
[[extensions.targeting]]
module = "./src/Checkout.tsx"
target = "purchase.checkout.delivery-address.render-before"

[extensions.capabilities]
protected_customer_data = true
```

## Customization

### Adding More Cities
To add more cities, edit the `ISRAELI_CITIES` array in `src/Checkout.tsx`:

```typescript
const ISRAELI_CITIES = [
  { value: "", label: "בחר עיר" },
  { value: "עיר חדשה", label: "עיר חדשה" },
  // ... existing cities
];
```

### Changing Languages
Add or modify translation files in the `locales/` directory following the existing pattern.

## Best Practices Followed

1. **Performance**: Only renders for Israeli addresses
2. **Accessibility**: Uses Shopify's native Select component
3. **User Experience**: Clear Hebrew labels and placeholders
4. **Error Handling**: Graceful fallbacks for API errors
5. **Type Safety**: TypeScript implementation with proper types

## Troubleshooting

### Extension Not Showing
- Verify the country is set to Israel (IL)
- Check that `protected_customer_data` capability is enabled
- Ensure the extension is properly deployed

### City Not Updating
- Check browser console for API errors
- Verify shipping address permissions
- Ensure the selected city value is not empty

## Support

For issues or questions:
1. Check the browser console for errors
2. Verify Shopify CLI version compatibility
3. Review Shopify's Checkout UI Extensions documentation
