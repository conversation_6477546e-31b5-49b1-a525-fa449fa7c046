import {
  reactExtension,
  Banner,
  BlockStack,
  Select,
  Text,
  useApplyShippingAddressChange,
  useShippingAddress,
  useInstructions,
} from "@shopify/ui-extensions-react/checkout";
import { useState, useEffect } from "react";

// Israeli cities list
const ISRAELI_CITIES = [
  { value: "", label: "בחר עיר" },
  { value: "תל אביב", label: "תל אביב" },
  { value: "ירושלים", label: "ירושלים" },
  { value: "חיפה", label: "חיפה" },
  { value: "ראשון לציון", label: "ראשו<PERSON> לציון" },
  { value: "אשדוד", label: "אשדוד" },
  { value: "נתניה", label: "נתניה" },
  { value: "באר שבע", label: "באר שבע" },
  { value: "בני ברק", label: "בני ברק" },
  { value: "חולון", label: "חולון" },
  { value: "רמת גן", label: "רמת גן" },
  { value: "אשקלון", label: "אש<PERSON><PERSON><PERSON><PERSON>" },
  { value: "רחובות", label: "רחובות" },
  { value: "בת ים", label: "בת ים" },
  { value: "כפר סבא", label: "כפר סבא" },
  { value: "הרצליה", label: "הרצליה" },
  { value: "חדרה", label: "חדרה" },
  { value: "מודיעין", label: "מודיעין" },
  { value: "נצרת", label: "נצרת" },
  { value: "לוד", label: "לוד" },
  { value: "רעננה", label: "רעננה" },
  { value: "רמלה", label: "רמלה" },
  { value: "פתח תקווה", label: "פתח תקווה" },
  { value: "גבעתיים", label: "גבעתיים" },
  { value: "קריית גת", label: "קריית גת" },
  { value: "נהריה", label: "נהריה" },
  { value: "קריית מוצקין", label: "קריית מוצקין" },
  { value: "קריית ביאליק", label: "קריית ביאליק" },
  { value: "קריית ים", label: "קריית ים" },
  { value: "עכו", label: "עכו" },
  { value: "אילת", label: "אילת" },
  { value: "טבריה", label: "טבריה" },
  { value: "צפת", label: "צפת" },
  { value: "דימונה", label: "דימונה" },
  { value: "אריאל", label: "אריאל" },
  { value: "מעלה אדומים", label: "מעלה אדומים" },
  { value: "בית שמש", label: "בית שמש" },
  { value: "כרמיאל", label: "כרמיאל" },
  { value: "יקנעם", label: "יקנעם" },
  { value: "מגדל העמק", label: "מגדל העמק" },
];

// 1. Choose an extension target for delivery address
export default reactExtension("purchase.checkout.delivery-address.render-before", () => (
  <Extension />
));

function Extension() {
  const instructions = useInstructions();
  const applyShippingAddressChange = useApplyShippingAddressChange();
  const shippingAddress = useShippingAddress();

  const [selectedCity, setSelectedCity] = useState("");

  // Initialize selected city from current shipping address
  useEffect(() => {
    if (shippingAddress?.city) {
      setSelectedCity(shippingAddress.city);
    }
  }, [shippingAddress]);

  // 2. Check instructions for feature availability
  if (!instructions.delivery?.canSelectCustomAddress) {
    // For checkouts where custom address selection is not allowed
    return (
      <Banner title="City Selection" status="warning">
        Custom address selection is not supported in this checkout
      </Banner>
    );
  }

  // Only show for Israel
  if (shippingAddress?.countryCode !== "IL") {
    return null;
  }

  // 3. Render Israeli city selector
  return (
    <BlockStack spacing="tight">
      <Text size="medium" emphasis="bold">
        בחר עיר
      </Text>
      <Select
        label="עיר"
        options={ISRAELI_CITIES}
        value={selectedCity}
        onChange={onCityChange}
        placeholder="בחר עיר מהרשימה"
      />
    </BlockStack>
  );

  function onCityChange(city: string) {
    setSelectedCity(city);

    if (city && city !== "") {
      // Update the shipping address with the selected city
      applyShippingAddressChange({
        type: "updateShippingAddress",
        address: {
          city: city,
        },
      }).then((result) => {
        console.log("City change result:", result);

        if (result.type === "error") {
          console.error("Failed to update city:", result.errors);
        }
      }).catch((error) => {
        console.error("Error updating city:", error);
      });
    }
  }
}